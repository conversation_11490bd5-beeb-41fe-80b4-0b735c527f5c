import { spawn } from "child_process"
import { promises as fs } from "fs"
import path from "path"
import { randomUUID } from "crypto"

export interface DownloadOptions {
  url: string
  quality: string
  format: string
  onProgress?: (progress: number) => void
}

export interface DownloadResult {
  success: boolean
  filePath?: string
  fileName?: string
  error?: string
  fileSize?: number
}

export class YtDlpDownloader {
  private downloadsDir: string

  constructor() {
    // Create downloads directory in the project root
    this.downloadsDir = path.join(process.cwd(), "downloads")
    this.ensureDownloadsDir()
  }

  private async ensureDownloadsDir() {
    try {
      await fs.access(this.downloadsDir)
    } catch {
      await fs.mkdir(this.downloadsDir, { recursive: true })
    }
  }

  async downloadVideo(options: DownloadOptions): Promise<DownloadResult> {
    const { url, quality, format, onProgress } = options
    const downloadId = randomUUID()
    const outputTemplate = path.join(this.downloadsDir, `${downloadId}.%(ext)s`)

 
    return new Promise((resolve) => {
      // yt-dlp command arguments
      const args = [
        url,
        "--output",
        outputTemplate,
        "--format",
        this.getFormatSelector(quality, format),
        "--no-playlist",
        "--cookies",
        "/app/cookies.txt", 
        "--write-info-json",
        "--progress-template",
        "%(progress._percent_str)s",
      ]

      console.log("Starting yt-dlp with args:", args)

      const ytDlp = spawn("yt-dlp", args, {
        stdio: ["pipe", "pipe", "pipe"],
      })

      let outputFilePath = ""
      let fileName = ""

      ytDlp.stdout.on("data", (data) => {
        const output = data.toString()
        console.log("yt-dlp stdout:", output)

        // Parse progress
        const progressMatch = output.match(/(\d+(?:\.\d+)?)%/)
        if (progressMatch && onProgress) {
          const progress = Number.parseFloat(progressMatch[1])
          onProgress(progress)
        }

        // Extract output file path
        const fileMatch = output.match(/\[download\] Destination: (.+)/)
        if (fileMatch) {
          outputFilePath = fileMatch[1]
          fileName = path.basename(outputFilePath)
        }
      })

      ytDlp.stderr.on("data", (data) => {
        const error = data.toString()
        console.error("yt-dlp stderr:", error)
      })

      ytDlp.on("close", async (code) => {
        if (code === 0 && outputFilePath) {
          try {
            const stats = await fs.stat(outputFilePath)
            resolve({
              success: true,
              filePath: outputFilePath,
              fileName: fileName,
              fileSize: stats.size,
            })
          } catch (error) {
            resolve({
              success: false,
              error: `File not found after download: ${error}`,
            })
          }
        } else {
          resolve({
            success: false,
            error: `yt-dlp exited with code ${code}`,
          })
        }
      })

      ytDlp.on("error", (error) => {
        resolve({
          success: false,
          error: `Failed to start yt-dlp: ${error.message}`,
        })
      })
    })
  }

  private getFormatSelector(quality: string, format: string): string {
    // Map quality to yt-dlp format selectors
    const qualityMap: Record<string, string> = {
      "2160p": "best[height<=2160]",
      "1440p": "best[height<=1440]",
      "1080p": "best[height<=1080]",
      "720p": "best[height<=720]",
      "480p": "best[height<=480]",
      "360p": "best[height<=360]",
    }

    const formatSelector = qualityMap[quality] || "best"

    if (format === "mp3") {
      return "bestaudio/best"
    }

    return `${formatSelector}[ext=${format}]/best[ext=${format}]/${formatSelector}`
  }

  async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = await fs.readdir(this.downloadsDir)
      const now = Date.now()
      const maxAge = maxAgeHours * 60 * 60 * 1000

      for (const file of files) {
        const filePath = path.join(this.downloadsDir, file)
        const stats = await fs.stat(filePath)

        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath)
          console.log(`Cleaned up old file: ${file}`)
        }
      }
    } catch (error) {
      console.error("Error cleaning up files:", error)
    }
  }
}

// Singleton instance
export const ytDlpDownloader = new YtDlpDownloader()
