import { spawn } from "child_process"
import { promises as fs } from "fs"
import path from "path"
import { randomUUID } from "crypto"

export interface DownloadOptions {
  url: string
  quality: string
  format: string
  onProgress?: (progress: number) => void
}

export interface DownloadResult {
  success: boolean
  filePath?: string
  fileName?: string
  error?: string
  fileSize?: number
}

export class YtDlpDownloader {
  private downloadsDir: string

  constructor() {
    // Create downloads directory in the project root
    this.downloadsDir = path.join(process.cwd(), "downloads")
    this.ensureDownloadsDir()
  }

  private async ensureDownloadsDir() {
    try {
      await fs.access(this.downloadsDir)
    } catch {
      await fs.mkdir(this.downloadsDir, { recursive: true })
    }
  }

  async downloadVideo(options: DownloadOptions): Promise<DownloadResult> {
    const { url, quality, format, onProgress } = options
    const downloadId = randomUUID()

    // Try multiple download strategies
    const strategies = [
      // Strategy 1: With cookies and android client
      {
        name: "cookies_android",
        args: this.buildArgs(url, downloadId, quality, format, "android", true)
      },
      // Strategy 2: Without cookies, android client
      {
        name: "no_cookies_android",
        args: this.buildArgs(url, downloadId, quality, format, "android", false)
      },
      // Strategy 3: Web client without cookies
      {
        name: "web_no_cookies",
        args: this.buildArgs(url, downloadId, quality, format, "web", false)
      }
    ]

    for (const strategy of strategies) {
      console.log(`Trying download strategy: ${strategy.name}`)
      const result = await this.attemptDownload(strategy.args, downloadId, onProgress)

      if (result.success) {
        return result
      }

      console.log(`Strategy ${strategy.name} failed: ${result.error}`)
    }

    return {
      success: false,
      error: "All download strategies failed. YouTube may be blocking downloads."
    }
  }

  private buildArgs(url: string, downloadId: string, quality: string, format: string, client: string, useCookies: boolean): string[] {
    const outputTemplate = path.join(this.downloadsDir, `${downloadId}.%(ext)s`)
    const cookiesPath = path.join(process.cwd(), "cookies.txt")

    const baseArgs = [
      url,
      "--output",
      outputTemplate,
      "--format",
      this.getFormatSelector(quality, format),
      "--no-playlist",
      "--write-info-json",
      "--progress-template",
      "%(progress._percent_str)s",
      "--no-check-certificates",
      "--extractor-retries",
      "2",
      "--fragment-retries",
      "2",
      "--retry-sleep",
      "1",
      "--user-agent",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ]

    if (useCookies) {
      baseArgs.push("--cookies", cookiesPath)
    }

    if (client === "android") {
      baseArgs.push("--extractor-args", "youtube:player_client=android")
    }

    return baseArgs
  }

  private async attemptDownload(args: string[], downloadId: string, onProgress?: (progress: number) => void): Promise<DownloadResult> {
    return new Promise((resolve) => {

      console.log("Starting yt-dlp with args:", args)

      const ytDlp = spawn("yt-dlp", args, {
        stdio: ["pipe", "pipe", "pipe"],
      })

      let outputFilePath = ""
      let fileName = ""

      let errorOutput = ""

      ytDlp.stdout.on("data", (data) => {
        const output = data.toString()
        console.log("yt-dlp stdout:", output)

        // Parse progress
        const progressMatch = output.match(/(\d+(?:\.\d+)?)%/)
        if (progressMatch && onProgress) {
          const progress = Number.parseFloat(progressMatch[1])
          onProgress(progress)
        }

        // Extract output file path - look for both destination and final file
        const fileMatch = output.match(/\[download\] Destination: (.+)/) ||
                         output.match(/\[download\] (.+) has already been downloaded/)
        if (fileMatch) {
          outputFilePath = fileMatch[1]
          fileName = path.basename(outputFilePath)
        }

        // Also check for merge messages
        const mergeMatch = output.match(/\[Merger\] Merging formats into "(.+)"/)
        if (mergeMatch) {
          outputFilePath = mergeMatch[1]
          fileName = path.basename(outputFilePath)
        }
      })

      ytDlp.stderr.on("data", (data) => {
        const error = data.toString()
        errorOutput += error
        console.error("yt-dlp stderr:", error)
      })

      ytDlp.on("close", async (code) => {
        console.log(`yt-dlp process exited with code: ${code}`)

        if (code === 0) {
          // Try to find the downloaded file
          if (outputFilePath) {
            try {
              const stats = await fs.stat(outputFilePath)
              console.log(`Successfully downloaded: ${outputFilePath} (${stats.size} bytes)`)
              resolve({
                success: true,
                filePath: outputFilePath,
                fileName: fileName,
                fileSize: stats.size,
              })
              return
            } catch (error) {
              console.error(`File not found at expected path: ${outputFilePath}`, error)
            }
          }

          // If no specific file path, try to find any downloaded file in the directory
          try {
            const files = await fs.readdir(this.downloadsDir)
            const downloadedFile = files.find(file => file.startsWith(downloadId) && !file.endsWith('.info.json'))

            if (downloadedFile) {
              const filePath = path.join(this.downloadsDir, downloadedFile)
              const stats = await fs.stat(filePath)
              console.log(`Found downloaded file: ${filePath} (${stats.size} bytes)`)
              resolve({
                success: true,
                filePath: filePath,
                fileName: downloadedFile,
                fileSize: stats.size,
              })
              return
            }
          } catch (error) {
            console.error("Error searching for downloaded files:", error)
          }

          resolve({
            success: false,
            error: "Download completed but no output file found",
          })
        } else {
          const errorMessage = errorOutput.trim() || `yt-dlp exited with code ${code}`
          console.error(`Download failed: ${errorMessage}`)
          resolve({
            success: false,
            error: errorMessage,
          })
        }
      })

      ytDlp.on("error", (error) => {
        resolve({
          success: false,
          error: `Failed to start yt-dlp: ${error.message}`,
        })
      })
    })
  }

  private getFormatSelector(quality: string, format: string): string {
    if (format === "mp3") {
      return "bestaudio[ext=m4a]/bestaudio/best"
    }

    // Simplified format selector that works better with current YouTube restrictions
    const heightMap: Record<string, string> = {
      "2160p": "2160",
      "1440p": "1440",
      "1080p": "1080",
      "720p": "720",
      "480p": "480",
      "360p": "360",
    }

    const maxHeight = heightMap[quality] || "720"

    // Try multiple format combinations to increase success rate
    return `best[height<=${maxHeight}]/best[height<=${maxHeight}][ext=mp4]/best[ext=mp4]/best`
  }

  async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = await fs.readdir(this.downloadsDir)
      const now = Date.now()
      const maxAge = maxAgeHours * 60 * 60 * 1000

      for (const file of files) {
        const filePath = path.join(this.downloadsDir, file)
        const stats = await fs.stat(filePath)

        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath)
          console.log(`Cleaned up old file: ${file}`)
        }
      }
    } catch (error) {
      console.error("Error cleaning up files:", error)
    }
  }
}

// Singleton instance
export const ytDlpDownloader = new YtDlpDownloader()
