# VideoBoom

A fast, free, and easy-to-use YouTube video downloader built with Next.js and yt-dlp.

## Features

- Download YouTube videos in multiple qualities (360p to 2160p)
- Support for MP4 and MP3 formats
- Real-time download progress tracking
- Clean and modern user interface
- Multiple fallback strategies for reliable downloads

## Getting Started

1. Install dependencies:

   ```bash
   npm install
   ```

2. Install yt-dlp:

   ```bash
   pip install yt-dlp
   ```

3. Run the development server:

   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Requirements

- Node.js 18+
- Python 3.7+
- yt-dlp

## License

MIT
